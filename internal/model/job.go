package model

import (
	"time"

	"github.com/google/uuid"
)

// Job 任务模型
type Job struct {
	BaseModel
	Name      string    `gorm:"size:100;not null" json:"name"`
	Type      string    `gorm:"size:20;not null" json:"type"` // spark, flink
	Status    string    `gorm:"size:20;default:'pending'" json:"status"`
	Namespace string    `gorm:"size:100;default:'default'" json:"namespace"`
	ClusterID uuid.UUID `gorm:"type:char(36)" json:"cluster_id"`
	Cluster   Cluster   `gorm:"foreignKey:ClusterID" json:"cluster"`
	AccountID string    `gorm:"type:char(36)" json:"account_id"`
	Config    string    `gorm:"type:text" json:"config"` // JSON 格式的配置
	Command   string    `gorm:"size:500" json:"command"`
	Args      string    `gorm:"size:1000" json:"args"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Runtime   int       `json:"runtime"` // 运行时长（秒）
	Logs      []string  `gorm:"-" json:"logs"`
}

// JobStatus 任务状态常量
const (
	JobStatusPending   = "pending"
	JobStatusRunning   = "running"
	JobStatusCompleted = "completed"
	JobStatusFailed    = "failed"
	JobStatusCancelled = "cancelled"
)

// TableName 指定表名
func (Job) TableName() string {
	return "jobs"
}
