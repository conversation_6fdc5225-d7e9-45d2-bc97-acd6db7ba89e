package model

import (
	"time"

	"github.com/google/uuid"
)

// Workflow 工作流模型
type Workflow struct {
	BaseModel
	Name        string    `gorm:"size:100;not null" json:"name"`
	Description string    `gorm:"size:500" json:"description"`
	Status      string    `gorm:"size:20;default:'pending'" json:"status"` // pending, running, completed, failed
	ClusterID   uuid.UUID `gorm:"type:char(36)" json:"cluster_id"`
	Cluster     Cluster   `gorm:"foreignKey:ClusterID" json:"cluster"`
	AccountID   string    `gorm:"type:char(36)" json:"account_id"`
	Namespace   string    `gorm:"size:100;default:'default'" json:"namespace"`
	Definition  string    `gorm:"type:text" json:"definition"` // JSON 格式的工作流定义
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
	ResourceID  string    `gorm:"size:100" json:"resource_id"` // 工作流资源ID
}

// WorkflowStatus 工作流状态常量
const (
	WorkflowStatusPending   = "pending"
	WorkflowStatusRunning   = "running"
	WorkflowStatusCompleted = "completed"
	WorkflowStatusFailed    = "failed"
	WorkflowStatusCancelled = "cancelled"
)

// TableName 指定表名
func (Workflow) TableName() string {
	return "workflows"
}
