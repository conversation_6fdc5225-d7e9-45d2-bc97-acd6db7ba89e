package v1

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/internal/service"
)

// WorkflowAPI 工作流 API 处理程序
type WorkflowAPI struct {
	workflowService service.WorkflowService
}

// NewWorkflowAPI 创建工作流 API 处理程序
func NewWorkflowAPI(workflowService service.WorkflowService) *WorkflowAPI {
	return &WorkflowAPI{
		workflowService: workflowService,
	}
}

// CreateWorkflowRequest 创建工作流请求
type CreateWorkflowRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	ClusterID   string `json:"cluster_id" binding:"required"`
	Namespace   string `json:"namespace"`
	Definition  string `json:"definition" binding:"required"`
}

// UpdateWorkflowRequest 更新工作流请求
type UpdateWorkflowRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Namespace   string `json:"namespace"`
	Definition  string `json:"definition"`
}

// UpdateWorkflowStatusRequest 更新工作流状态请求
type UpdateWorkflowStatusRequest struct {
	Status string `json:"status" binding:"required"`
}

// ArgoWebUIURLResponse Argo Web UI URL 响应
type ArgoWebUIURLResponse struct {
	ClusterID   string `json:"cluster_id"`
	WebUIURL    string `json:"web_ui_url"`
	Description string `json:"description"`
}

// AsyncWorkflowResponse 异步工作流响应
type AsyncWorkflowResponse struct {
	WorkflowID   string `json:"workflow_id"`
	WorkflowName string `json:"workflow_name"`
	ArgoWorkflow string `json:"argo_workflow"`
	Status       string `json:"status"`
	Message      string `json:"message"`
}

// Create 创建工作流
// @Summary 创建工作流
// @Description 创建新工作流
// @Tags 工作流
// @Accept json
// @Produce json
// @Param workflow body CreateWorkflowRequest true "工作流信息"
// @Success 201 {object} model.Workflow
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows [post]
func (api *WorkflowAPI) Create(c *gin.Context) {
	var req CreateWorkflowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数"})
		return
	}

	// 从上下文中获取用户ID
	accountID, exists := c.Get("account_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "未授权"})
		return
	}

	// 解析集群ID
	clusterID, err := uuid.Parse(req.ClusterID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
		return
	}

	// 设置默认命名空间
	namespace := req.Namespace
	if namespace == "" {
		namespace = "default"
	}

	workflow, err := api.workflowService.Create(req.Name, req.Description, namespace, accountID.(string), clusterID, req.Definition)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, workflow)
}

// GetByID 通过ID获取工作流
// @Summary 通过ID获取工作流
// @Description 通过ID获取工作流信息
// @Tags 工作流
// @Accept json
// @Produce json
// @Param id path string true "工作流ID"
// @Success 200 {object} model.Workflow
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/{id} [get]
func (api *WorkflowAPI) GetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的工作流ID"})
		return
	}

	workflow, err := api.workflowService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "工作流不存在"})
		return
	}

	c.JSON(http.StatusOK, workflow)
}

// Update 更新工作流
// @Summary 更新工作流
// @Description 更新工作流信息
// @Tags 工作流
// @Accept json
// @Produce json
// @Param id path string true "工作流ID"
// @Param workflow body UpdateWorkflowRequest true "工作流信息"
// @Success 200 {object} model.Workflow
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/{id} [put]
func (api *WorkflowAPI) Update(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的工作流ID"})
		return
	}

	var req UpdateWorkflowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数"})
		return
	}

	workflow, err := api.workflowService.Update(id, req.Name, req.Description, req.Namespace, req.Definition)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, workflow)
}

// UpdateStatus 更新工作流状态
// @Summary 更新工作流状态
// @Description 更新工作流状态
// @Tags 工作流
// @Accept json
// @Produce json
// @Param id path string true "工作流ID"
// @Param workflow body UpdateWorkflowStatusRequest true "工作流状态"
// @Success 200 {object} model.Workflow
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/{id}/status [put]
func (api *WorkflowAPI) UpdateStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的工作流ID"})
		return
	}

	var req UpdateWorkflowStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数"})
		return
	}

	workflow, err := api.workflowService.UpdateStatus(id, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, workflow)
}

// Delete 删除工作流
// @Summary 删除工作流
// @Description 删除指定的工作流
// @Tags 工作流
// @Accept json
// @Produce json
// @Param id path string true "工作流ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/{id} [delete]
func (api *WorkflowAPI) Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的工作流ID"})
		return
	}

	if err := api.workflowService.Delete(id); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, Response{Message: "工作流删除成功"})
}

// GetArgoWebUIURL 获取指定集群的 Argo Workflows Web UI URL
// @Summary 获取 Argo Workflows Web UI URL
// @Description 获取指定集群的 Argo Workflows Web UI 访问地址
// @Tags 工作流
// @Accept json
// @Produce json
// @Param cluster_id query string true "集群ID"
// @Success 200 {object} ArgoWebUIURLResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/argo-webui-url [get]
func (api *WorkflowAPI) GetArgoWebUIURL(c *gin.Context) {
	clusterID := c.Query("cluster_id")
	if clusterID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "cluster_id 参数是必需的"})
		return
	}

	url, err := api.workflowService.GetArgoWebUIURL(clusterID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"cluster_id":  clusterID,
		"web_ui_url":  url,
		"description": "Argo Workflows Web UI 访问地址",
	})
}

// List 获取工作流列表
// @Summary 获取工作流列表
// @Description 获取工作流列表
// @Tags 工作流
// @Accept json
// @Produce json
// @Param offset query int false "分页偏移量"
// @Param limit query int false "每页数量"
// @Param cluster_id query string false "集群ID"
// @Param account_id query string false "所有者ID"
// @Param status query string false "工作流状态"
// @Success 200 {array} model.Workflow
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows [get]
func (api *WorkflowAPI) List(c *gin.Context) {
	// 获取分页参数
	offsetStr := c.DefaultQuery("offset", "0")
	limitStr := c.DefaultQuery("limit", "10")

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的偏移量参数"})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的页大小参数"})
		return
	}

	// 获取过滤条件
	clusterIDStr := c.Query("cluster_id")
	accountID := c.Query("account_id")
	status := c.Query("status")

	// 解析过滤条件
	var clusterID *uuid.UUID

	if clusterIDStr != "" {
		id, err := uuid.Parse(clusterIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
			return
		}
		clusterID = &id
	}

	// 获取工作流列表
	workflows, total, err := api.workflowService.List(offset, limit, clusterID, accountID, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"total": total,
		"data":  workflows,
	})
}

// Submit 提交工作流
// @Summary 提交工作流
// @Description 提交工作流到集群
// @Tags 工作流
// @Accept json
// @Produce json
// @Param id path string true "工作流ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/{id}/submit [post]
func (api *WorkflowAPI) Submit(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的工作流ID"})
		return
	}

	if err := api.workflowService.Submit(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, Response{Message: "工作流提交成功"})
}

// Cancel 取消工作流
// @Summary 取消工作流
// @Description 取消正在运行的工作流
// @Tags 工作流
// @Accept json
// @Produce json
// @Param id path string true "工作流ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/{id}/cancel [post]
func (api *WorkflowAPI) Cancel(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的工作流ID"})
		return
	}

	if err := api.workflowService.Cancel(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, Response{Message: "工作流取消成功"})
}

// 异步集群操作相关请求结构

// CreateClusterAsyncRequest 异步创建集群请求
type CreateClusterAsyncRequest struct {
	Name         string   `json:"name" binding:"required"`
	Description  string   `json:"description"`
	KceClusterID string   `json:"kce_cluster_id" binding:"required"`
	AccountID    string   `json:"account_id" binding:"required"`
	Services     []string `json:"services"`
	AK           string   `json:"ak"`
	SK           string   `json:"sk"`
	LogDirectory string   `json:"log_directory"`
}

// DeleteClusterAsyncRequest 异步删除集群请求
type DeleteClusterAsyncRequest struct {
	ClusterID string `json:"cluster_id" binding:"required"`
	AccountID string `json:"account_id" binding:"required"`
}

// OperatorAsyncRequest 异步Operator操作请求
type OperatorAsyncRequest struct {
	ClusterID    string `json:"cluster_id" binding:"required"`
	OperatorType string `json:"operator_type" binding:"required"`
	AccountID    string `json:"account_id" binding:"required"`
}

// RetryWorkflowRequest 重试工作流请求
type RetryWorkflowRequest struct {
	StepName string `json:"step_name,omitempty"`
}

// CreateClusterAsync 异步创建集群
// @Summary 异步创建集群
// @Description 异步创建新集群并可选择部署指定的服务，使用Argo Workflows执行，返回工作流ID用于查询进度
// @Tags 工作流
// @Accept json
// @Produce json
// @Param cluster body CreateClusterAsyncRequest true "集群信息"
// @Success 202 {object} AsyncWorkflowResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/clusters [post]
func (api *WorkflowAPI) CreateClusterAsync(c *gin.Context) {
	var req CreateClusterAsyncRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数: " + err.Error()})
		return
	}

	// 调用异步创建集群服务
	workflow, err := api.workflowService.CreateClusterAsync(
		req.Name,
		req.Description,
		req.KceClusterID,
		req.AccountID,
		req.Services,
		req.AK,
		req.SK,
		req.LogDirectory,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "创建异步集群工作流失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"workflow_id":   workflow.ID,
		"workflow_name": workflow.Name,
		"argo_workflow": workflow.ResourceID,
		"status":        workflow.Status,
		"message":       "集群创建工作流已提交，请使用workflow_id查询进度",
	})
}

// DeleteClusterAsync 异步删除集群
// @Summary 异步删除集群
// @Description 异步删除指定的集群，并自动卸载所有已安装的Operator，使用Argo Workflows执行，返回工作流ID用于查询进度
// @Tags 工作流
// @Accept json
// @Produce json
// @Param request body DeleteClusterAsyncRequest true "删除请求"
// @Success 202 {object} AsyncWorkflowResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/clusters/delete [post]
func (api *WorkflowAPI) DeleteClusterAsync(c *gin.Context) {
	var req DeleteClusterAsyncRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数: " + err.Error()})
		return
	}

	// 验证集群ID格式
	clusterUUID, err := uuid.Parse(req.ClusterID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID格式"})
		return
	}

	// 调用异步删除集群服务
	workflow, err := api.workflowService.DeleteClusterAsync(clusterUUID, req.AccountID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "创建异步集群删除工作流失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"workflow_id":   workflow.ID,
		"workflow_name": workflow.Name,
		"argo_workflow": workflow.ResourceID,
		"status":        workflow.Status,
		"message":       "集群删除工作流已提交，请使用workflow_id查询进度",
	})
}

// InstallOperatorAsync 异步安装Operator
// @Summary 异步安装Operator
// @Description 在指定的集群上异步安装指定类型的Operator，使用Argo Workflows执行，返回工作流ID用于查询进度
// @Tags 工作流
// @Accept json
// @Produce json
// @Param request body OperatorAsyncRequest true "安装请求"
// @Success 202 {object} AsyncWorkflowResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/operators/install [post]
func (api *WorkflowAPI) InstallOperatorAsync(c *gin.Context) {
	var req OperatorAsyncRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数: " + err.Error()})
		return
	}

	// 验证集群ID格式
	clusterUUID, err := uuid.Parse(req.ClusterID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID格式"})
		return
	}

	// 调用异步安装Operator服务
	workflow, err := api.workflowService.InstallOperatorAsync(clusterUUID, req.OperatorType, req.AccountID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "创建异步Operator安装工作流失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"workflow_id":   workflow.ID,
		"workflow_name": workflow.Name,
		"argo_workflow": workflow.ResourceID,
		"status":        workflow.Status,
		"message":       "Operator安装工作流已提交，请使用workflow_id查询进度",
	})
}

// UninstallOperatorAsync 异步卸载Operator
// @Summary 异步卸载Operator
// @Description 从指定的集群上异步卸载指定类型的Operator，使用Argo Workflows执行，返回工作流ID用于查询进度
// @Tags 工作流
// @Accept json
// @Produce json
// @Param request body OperatorAsyncRequest true "卸载请求"
// @Success 202 {object} AsyncWorkflowResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/operators/uninstall [post]
func (api *WorkflowAPI) UninstallOperatorAsync(c *gin.Context) {
	var req OperatorAsyncRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数: " + err.Error()})
		return
	}

	// 验证集群ID格式
	clusterUUID, err := uuid.Parse(req.ClusterID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID格式"})
		return
	}

	// 调用异步卸载Operator服务
	workflow, err := api.workflowService.UninstallOperatorAsync(clusterUUID, req.OperatorType, req.AccountID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "创建异步Operator卸载工作流失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"workflow_id":   workflow.ID,
		"workflow_name": workflow.Name,
		"argo_workflow": workflow.ResourceID,
		"status":        workflow.Status,
		"message":       "Operator卸载工作流已提交，请使用workflow_id查询进度",
	})
}

// RetryWorkflow 重试工作流
// @Summary 重试工作流
// @Description 重试失败的工作流或特定步骤，支持重试整个工作流或单个步骤
// @Tags 工作流
// @Accept json
// @Produce json
// @Param id path string true "工作流ID"
// @Param request body RetryWorkflowRequest false "重试请求"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/{id}/retry [post]
func (api *WorkflowAPI) RetryWorkflow(c *gin.Context) {
	idStr := c.Param("id")
	workflowID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的工作流ID"})
		return
	}

	var req RetryWorkflowRequest
	if err := c.ShouldBindJSON(&req); err != nil && err.Error() != "EOF" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数: " + err.Error()})
		return
	}

	ctx := c.Request.Context()

	// 如果指定了步骤名称，重试特定步骤；否则重试整个工作流
	if req.StepName != "" {
		err = api.workflowService.RetryWorkflowStep(ctx, workflowID, req.StepName)
		if err != nil {
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "重试工作流步骤失败: " + err.Error()})
			return
		}
		c.JSON(http.StatusOK, Response{Message: "工作流步骤重试成功"})
	} else {
		err = api.workflowService.RetryWorkflow(ctx, workflowID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "重试工作流失败: " + err.Error()})
			return
		}
		c.JSON(http.StatusOK, Response{Message: "工作流重试成功"})
	}
}

// SyncWorkflowStatus 同步工作流状态
// @Summary 同步工作流状态
// @Description 从Argo Workflows同步工作流状态到数据库
// @Tags 工作流
// @Accept json
// @Produce json
// @Param id path string true "工作流ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/workflows/{id}/sync [post]
func (api *WorkflowAPI) SyncWorkflowStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的工作流ID"})
		return
	}

	if err := api.workflowService.SyncWorkflowStatus(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "工作流状态同步成功"})
}

// CleanupCompletedPods 手动触发Pod清理
func (api *WorkflowAPI) CleanupCompletedPods(c *gin.Context) {
	clusterID := c.Query("cluster_id")
	if clusterID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "cluster_id参数是必需的"})
		return
	}

	if err := api.workflowService.CleanupCompletedPods(c.Request.Context(), clusterID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Pod清理完成"})
}

// SyncAllWorkflowStatuses 手动同步所有工作流状态
func (api *WorkflowAPI) SyncAllWorkflowStatuses(c *gin.Context) {
	if err := api.workflowService.SyncAllWorkflowStatuses(c.Request.Context()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "所有工作流状态同步完成"})
}

// StartPodMonitor 启动Pod监控
func (api *WorkflowAPI) StartPodMonitor(c *gin.Context) {
	api.workflowService.StartPodMonitor(c.Request.Context())
	c.JSON(http.StatusOK, gin.H{"message": "Pod监控已启动"})
}

// StopPodMonitor 停止Pod监控
func (api *WorkflowAPI) StopPodMonitor(c *gin.Context) {
	api.workflowService.StopPodMonitor()
	c.JSON(http.StatusOK, gin.H{"message": "Pod监控已停止"})
}
