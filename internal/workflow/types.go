package workflow

// 通用常量与类型定义

const (
	// ArgoWorkflowGroup Argo Workflow 的 API Group
	ArgoWorkflowGroup = "argoproj.io"
	// ArgoWorkflowVersion Argo Workflow 的 API Version
	ArgoWorkflowVersion = "v1alpha1"
	// ArgoWorkflowResource Argo Workflow 的资源名
	ArgoWorkflowResource = "workflows"
	// ArgoWorkflowNamespace Argo Workflow 的默认命名空间
	ArgoWorkflowNamespace = "argo"
)

// 在容器内使用 ServiceAccount 自动生成 kubeconfig 的脚本
const kubectlInClusterSetupShell = `
# 尝试基于容器内的 ServiceAccount 自动配置 kubectl 访问当前集群
if [ -n "$KUBERNETES_SERVICE_HOST" ] && [ -f "/var/run/secrets/kubernetes.io/serviceaccount/token" ]; then
  echo "[setup] 配置 kubectl 使用 in-cluster ServiceAccount 凭证"
  export K8S_HOST="https://${KUBERNETES_SERVICE_HOST}:${KUBERNETES_SERVICE_PORT:-443}"
  # 生成临时 kubeconfig 文件
  cat >/tmp/kubeconfig <<EOF
apiVersion: v1
kind: Config
clusters:
- cluster:
    certificate-authority: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    server: ${K8S_HOST}
  name: in-cluster
contexts:
- context:
    cluster: in-cluster
    user: in-cluster-user
  name: in-cluster
current-context: in-cluster
users:
- name: in-cluster-user
  user:
    token: $(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
EOF
  export KUBECONFIG=/tmp/kubeconfig
fi
`

// WorkflowDefinition 工作流定义结构
type WorkflowDefinition struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Labels      map[string]string `json:"labels,omitempty"`
	Spec        WorkflowSpec      `json:"spec"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

// WorkflowSpec 工作流规格
type WorkflowSpec struct {
	Entrypoint         string             `json:"entrypoint"`
	Templates          []WorkflowTemplate `json:"templates"`
	Arguments          *WorkflowInputs    `json:"arguments,omitempty"`
	ServiceAccountName string             `json:"serviceAccountName,omitempty"`
}

// WorkflowTemplate 工作流模板
type WorkflowTemplate struct {
	Name      string                 `json:"name"`
	Steps     [][]WorkflowStep       `json:"steps,omitempty"`
	Container *WorkflowContainerSpec `json:"container,omitempty"`
	Script    *WorkflowScriptSpec    `json:"script,omitempty"`
	Inputs    *WorkflowInputs        `json:"inputs,omitempty"`
	Outputs   *WorkflowOutputs       `json:"outputs,omitempty"`
}

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	Name      string          `json:"name"`
	Template  string          `json:"template"`
	Arguments *WorkflowInputs `json:"arguments,omitempty"`
	// When 条件执行表达式（Argo Workflows 支持的 when 表达式）
	When string `json:"when,omitempty"`
}

// WorkflowContainerSpec 容器规格
type WorkflowContainerSpec struct {
	Image   string   `json:"image"`
	Command []string `json:"command,omitempty"`
	Args    []string `json:"args,omitempty"`
	Env     []EnvVar `json:"env,omitempty"`
}

// WorkflowScriptSpec 脚本规格
type WorkflowScriptSpec struct {
	Image   string   `json:"image"`
	Source  string   `json:"source"`
	Command []string `json:"command,omitempty"`
}

// WorkflowInputs 工作流输入
type WorkflowInputs struct {
	Parameters []WorkflowParameter `json:"parameters,omitempty"`
}

// WorkflowOutputs 工作流输出
type WorkflowOutputs struct {
	Parameters []WorkflowParameter `json:"parameters,omitempty"`
}

// WorkflowParameter 工作流参数
type WorkflowParameter struct {
	Name  string `json:"name"`
	Value string `json:"value,omitempty"`
}

// EnvVar 环境变量
type EnvVar struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}
