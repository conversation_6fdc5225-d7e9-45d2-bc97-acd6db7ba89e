package workflow

// Flink Operator 安装相关工作流定义

// createFlinkOperatorWorkflow 创建 Flink Operator 安装工作流
func (m *ArgoWorkflowManager) createFlinkOperatorWorkflow(workflowName, clusterID string) WorkflowDefinition {
	return WorkflowDefinition{
		Name:      workflowName,
		Namespace: ArgoWorkflowNamespace,
		Labels: map[string]string{
			"app":        "nimbus",
			"operator":   "flink",
			"cluster-id": clusterID,
		},
		Spec: WorkflowSpec{
			Entrypoint:         "install-flink-operator",
			ServiceAccountName: "kubectl-admin-sa",
			Templates: []WorkflowTemplate{
				{Name: "install-flink-operator", Steps: [][]WorkflowStep{{
					{Name: "create-namespace", Template: "create-flink-namespace"},
				}, {
					{Name: "create-rbac", Template: "create-flink-rbac"},
				}, {
					{Name: "deploy-operator", Template: "deploy-flink-operator"},
				}, {
					{Name: "verify-deployment", Template: "verify-flink-deployment"},
				}}},
				// 其余模板在下方定义
				{Name: "create-flink-rbac", Script: &WorkflowScriptSpec{Image: "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3", Command: []string{"/bin/sh"}, Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e
kubectl apply -f - <<EOF
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: flink-operator
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "events", "configmaps", "secrets", "serviceaccounts"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["deployments", "deployments/finalizers", "replicasets"]
  verbs: ["*"]
- apiGroups: ["flink.apache.org"]
  resources: ["flinkdeployments", "flinkdeployments/status", "flinksessionjobs", "flinksessionjobs/status"]
  verbs: ["*"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["*"]
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: flink-operator
subjects:
- kind: ServiceAccount
  name: flink-operator
  namespace: kmrflink
roleRef:
  kind: ClusterRole
  name: flink-operator
  apiGroup: rbac.authorization.k8s.io
EOF`}},
				{Name: "deploy-flink-operator", Script: &WorkflowScriptSpec{Image: "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3", Command: []string{"/bin/sh"}, Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e
kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: flink-kubernetes-operator
  namespace: kmrflink
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: flink-kubernetes-operator
  template:
    metadata:
      labels:
        app.kubernetes.io/name: flink-kubernetes-operator
    spec:
      serviceAccountName: flink-operator
      containers:
      - name: flink-kubernetes-operator
        image: ghcr.io/apache/flink-kubernetes-operator:1.6.1
        ports:
        - containerPort: 9999
          name: metrics
        - containerPort: 9443
          name: webhook
        env:
        - name: WATCH_NAMESPACE
          value: ""
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: OPERATOR_NAME
          value: "flink-kubernetes-operator"
EOF`}},
				{Name: "verify-flink-deployment", Container: &WorkflowContainerSpec{Image: "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3", Command: []string{"kubectl"}, Args: []string{"wait", "--for=condition=available", "--timeout=300s", "deployment/flink-kubernetes-operator", "-n", "kmrflink"}}},
			},
		},
	}
}

// getFlinkOperatorInstallTemplates 获取 Flink Operator 安装模板集合
func (m *ArgoWorkflowManager) getFlinkOperatorInstallTemplates() []WorkflowTemplate {
	return []WorkflowTemplate{
		{
			Name: "install-flink-operator-template",
			Steps: [][]WorkflowStep{{
				{
					Name:     "deploy-flink-operator",
					Template: "deploy-flink-operator",
					Arguments: &WorkflowInputs{Parameters: []WorkflowParameter{
						{Name: "kce-cluster-id", Value: "{{inputs.parameters.kce-cluster-id}}"},
					}},
				},
			}},
			Inputs: &WorkflowInputs{Parameters: []WorkflowParameter{
				{Name: "cluster-id"},
				{Name: "kce-cluster-id"},
				{Name: "service-type"},
			}},
		},
		{
			Name: "deploy-flink-operator",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "部署 Flink Operator (KCE集群: {{inputs.parameters.kce-cluster-id}})..."

# 仅使用Helm仓库安装Flink Operator
echo "使用Helm仓库安装Flink Operator..."

# 添加并更新helm仓库
echo "添加Helm仓库..."
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update

# 清理可能存在的旧资源
echo "清理可能存在的旧资源..."
helm uninstall flink-operator -n kmrflink || true
kubectl delete namespace kmrflink || true

# 全新安装Flink Operator
echo "全新安装Flink Operator..."
helm install flink-operator kmr-on-kce/flink-operator \
  --namespace kmrflink \
  --create-namespace \
  --set image.repository=ghcr.io/apache/flink-kubernetes-operator \
  --set image.tag=1.6.1 \
  --wait --timeout=10m

echo "✓ Flink Operator安装完成"
`,
			},
			Inputs: &WorkflowInputs{Parameters: []WorkflowParameter{{Name: "kce-cluster-id"}}},
		},
	}
}
