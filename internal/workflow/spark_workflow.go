package workflow

// Spark Operator 安装相关工作流定义

// createSparkOperatorWorkflow 创建 Spark Operator 安装工作流
func (m *ArgoWorkflowManager) createSparkOperatorWorkflow(workflowName, clusterID string) WorkflowDefinition {
	return WorkflowDefinition{
		Name:      workflowName,
		Namespace: ArgoWorkflowNamespace,
		Labels: map[string]string{
			"app":        "nimbus",
			"operator":   "spark",
			"cluster-id": clusterID,
		},
		Spec: WorkflowSpec{
			Entrypoint:         "install-spark-operator",
			ServiceAccountName: "kubectl-admin-sa",
			Templates: []WorkflowTemplate{
				{
					Name: "install-spark-operator",
					Steps: [][]WorkflowStep{
						{
							{Name: "create-namespace", Template: "create-spark-namespace"},
						},
						{
							{Name: "install-crds", Template: "install-spark-crds"},
						},
						{
							{Name: "install-operator", Template: "install-spark-operator-helm"},
						},
						{
							{Name: "verify-deployment", Template: "verify-spark-deployment"},
						},
					},
				},
				// 其余模板在下方定义
				{Name: "install-spark-crds", Script: &WorkflowScriptSpec{Image: "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3", Command: []string{"/bin/sh"}, Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e
echo "安装 Spark Operator CRDs..."
# 省略CRD具体YAML，保持与原实现一致
`}},
				{Name: "create-spark-namespace", Script: &WorkflowScriptSpec{Image: "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3", Command: []string{"/bin/sh"}, Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e
kubectl create namespace kmrspark --dry-run=client -o yaml | kubectl apply -f -
`}},
				{Name: "install-spark-operator-helm", Script: &WorkflowScriptSpec{Image: "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3", Command: []string{"/bin/sh"}, Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update
helm uninstall spark-operator -n kmrspark || true
kubectl delete namespace kmrspark || true
helm install spark-operator kmr-on-kce/spark-operator \
  --namespace kmrspark \
  --create-namespace \
  --set image.repository=hub.kce.ksyun.com/bigdata-platform/spark-operator \
  --set image.tag=2.0.0-36 \
  --set sparkJobNamespaces[0]=kmrspark \
  --set uiService.enable=true \
  --wait --timeout=10m
`}},
				{Name: "verify-spark-deployment", Script: &WorkflowScriptSpec{Image: "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3", Command: []string{"/bin/sh"}, Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e
kubectl wait --for=condition=available --timeout=300s deployment/spark-operator -n kmrspark
`}},
			},
		},
	}
}

// getSparkOperatorInstallTemplates 获取 Spark Operator 安装模板集合
func (m *ArgoWorkflowManager) getSparkOperatorInstallTemplates() []WorkflowTemplate {
	return []WorkflowTemplate{
		{
			Name: "install-spark-operator-template",
			Steps: [][]WorkflowStep{
				{
					{
						Name:     "install-spark-operator",
						Template: "install-spark-operator-helm",
						Arguments: &WorkflowInputs{Parameters: []WorkflowParameter{
							{Name: "kce-cluster-id", Value: "{{inputs.parameters.kce-cluster-id}}"},
							{Name: "cluster-id", Value: "{{inputs.parameters.cluster-id}}"},
						}},
					},
				},
				{
					{
						Name:     "install-spark-history",
						Template: "install-spark-history-helm",
						Arguments: &WorkflowInputs{Parameters: []WorkflowParameter{
							{Name: "kce-cluster-id", Value: "{{inputs.parameters.kce-cluster-id}}"},
							{Name: "ak", Value: "{{inputs.parameters.ak}}"},
							{Name: "sk", Value: "{{inputs.parameters.sk}}"},
							{Name: "logDirectory", Value: "{{inputs.parameters.logDirectory}}"},
						}},
					},
				},
			},
			Inputs: &WorkflowInputs{Parameters: []WorkflowParameter{
				{Name: "cluster-id"},
				{Name: "kce-cluster-id"},
				{Name: "service-type"},
				{Name: "ak"},
				{Name: "sk"},
				{Name: "logDirectory"},
			}},
		},
		{
			Name: "install-spark-operator-helm",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始安装Spark Operator..."

# 添加并更新helm仓库
echo "添加Helm仓库..."
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update

# 彻底清理可能存在的旧资源
echo "彻底清理可能存在的旧资源..."

# 1. 先尝试卸载 Helm release
helm uninstall spark-operator -n kmrspark || true
sleep 5

# 2. 强制删除可能残留的资源
echo "强制删除残留资源..."
kubectl delete serviceaccount spark-operator -n kmrspark --ignore-not-found=true
kubectl delete clusterrole spark-operator --ignore-not-found=true
kubectl delete clusterrolebinding spark-operator --ignore-not-found=true
kubectl delete role spark-operator -n kmrspark --ignore-not-found=true
kubectl delete rolebinding spark-operator -n kmrspark --ignore-not-found=true
kubectl delete deployment spark-operator -n kmrspark --ignore-not-found=true
kubectl delete service spark-operator -n kmrspark --ignore-not-found=true

# 3. 删除并重新创建命名空间
kubectl delete namespace kmrspark --ignore-not-found=true
sleep 10
kubectl create namespace kmrspark

# 4. 全新安装Spark Operator（仅从helm repo拉取）
echo "全新安装Spark Operator..."

# 获取集群ID的前8位作为host前缀
CLUSTER_PREFIX=$(echo {{inputs.parameters.kce-cluster-id}} | cut -d- -f1)
echo "使用集群前缀: $CLUSTER_PREFIX"

helm install spark-operator kmr-on-kce/spark-operator \
  --namespace kmrspark \
  --set image.repository=hub.kce.ksyun.com/bigdata-platform/spark-operator \
  --set image.tag=2.0.0-36 \
  --set sparkJobNamespaces[0]=kmrspark \
  --set uiService.enable=true \
  --set ingressUrlFormat="$CLUSTER_PREFIX-spark-job.kmr-on-kce-pre.ksyun.com" \
  --set ingressClassName=kmr-on-kce \
  --wait --timeout=10m
echo "✓ Spark Operator安装完成"
`,
			},
			Inputs: &WorkflowInputs{Parameters: []WorkflowParameter{
				{Name: "kce-cluster-id"},
				{Name: "cluster-id"},
			}},
		},
		{
			Name: "install-spark-history-helm",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始安装Spark History..."

if [ -z "{{inputs.parameters.ak}}" ] || [ -z "{{inputs.parameters.sk}}" ] || [ -z "{{inputs.parameters.logDirectory}}" ]; then
  echo "未提供 AK/SK/logDirectory，跳过 Spark History 安装"
  exit 0
fi

echo "安装 Spark History Helm chart (远端仓库版本 0.1.3)"

# 准备 values
cat > /tmp/spark-history-values.yaml <<EOF
fullName: spark-history
rbac:
  roleName: spark
  roleBindingName: spark
replicaCount: 1
image:
  repository: hub.kce.ksyun.com/bigdata-platform/pyspark
  tag: v3.4.3-ksc1.3
  pullPolicy: Always
ks3:
  endpoint: ks3-cn-beijing-internal.ksyuncs.com
  AccessKey: {{inputs.parameters.ak}}
  AccessSecret: {{inputs.parameters.sk}}
  logDirectory: {{inputs.parameters.logDirectory}}/w-$(echo {{inputs.parameters.kce-cluster-id}} | tr -d '-' | cut -c1-10)
service:
  name: http
  externalPort: 80
  internalPort: 18080
  type: ClusterIP
ingress:
  host: $(echo {{inputs.parameters.kce-cluster-id}} | cut -d- -f1)-sparkhistory.kmr-on-kce-pre.ksyun.com
  ingressClass: kmr-on-kce
resources: {}
nodeSelector: {}
tolerations: []
EOF

# 使用远端仓库安装
echo "添加 kmr-on-kce 仓库..."
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update

# 1. 先尝试卸载 Helm release（避免旧残留影响）
helm uninstall spark-history -n kmrspark || true
sleep 3

# 2. 检查并创建命名空间
kubectl get namespace kmrspark || kubectl create namespace kmrspark

# 3. 确保 Spark Operator 的 ServiceAccount 存在
echo "检查 spark-operator-spark ServiceAccount..."
if ! kubectl get serviceaccount spark-operator-spark -n kmrspark >/dev/null 2>&1; then
  echo "创建 spark-operator-spark ServiceAccount..."
  kubectl create serviceaccount spark-operator-spark -n kmrspark
else
  echo "spark-operator-spark ServiceAccount 已存在"
fi

# 4. 安装 Spark History
helm upgrade --install spark-history kmr-on-kce/spark-history \
  --version 0.1.3 \
  -n kmrspark \
  -f /tmp/spark-history-values.yaml \
  --wait --timeout=10m

# 5. 验证安装结果
echo "验证 Spark History 安装..."
kubectl get pods -n kmrspark -l app=spark-history
kubectl get service -n kmrspark spark-history-service
kubectl get deployment -n kmrspark spark-history-deployment

echo "✓ Spark History 安装完成"
`,
			},
			Inputs: &WorkflowInputs{Parameters: []WorkflowParameter{
				{Name: "kce-cluster-id"},
				{Name: "ak"},
				{Name: "sk"},
				{Name: "logDirectory"},
			}},
		},
	}
}
