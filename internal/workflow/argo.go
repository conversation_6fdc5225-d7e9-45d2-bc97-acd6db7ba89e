package workflow

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	innerKubernetes "github.com/kingsoft/nimbus/internal/kubernetes"

	"github.com/kingsoft/nimbus/pkg/logger"
	"github.com/kingsoft/nimbus/pkg/utils"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

// ArgoWorkflowManager Argo Workflow 管理器
type ArgoWorkflowManager struct {
	clientManager *innerKubernetes.ClientManager
}

// NewArgoWorkflowManager 创建 Argo Workflow 管理器
func NewArgoWorkflowManager(clientManager *innerKubernetes.ClientManager) *ArgoWorkflowManager {
	return &ArgoWorkflowManager{
		clientManager: clientManager,
	}
}

// 类型与常量已迁移至 types.go

// SubmitWorkflow 提交工作流到 Argo
func (m *ArgoWorkflowManager) SubmitWorkflow(ctx context.Context, kceClusterID string, workflowDef WorkflowDefinition) (string, error) {
	// 获取 Kubernetes 客户端
	client, err := m.clientManager.GetClient(ctx, kceClusterID)
	if err != nil {
		return "", fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 创建动态客户端
	config, err := m.getRestConfig(ctx, kceClusterID)
	if err != nil {
		return "", fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return "", fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 确保 Argo 命名空间存在
	if err := m.ensureArgoNamespace(ctx, client, kceClusterID, workflowDef.Namespace); err != nil {
		return "", fmt.Errorf("确保 Argo 命名空间失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 生成唯一的工作流名称
	if workflowDef.Name == "" {
		workflowDef.Name = fmt.Sprintf("wf-%s", uuid.New().String()[:8])
	}

	// 设置默认命名空间
	if workflowDef.Namespace == "" {
		workflowDef.Namespace = ArgoWorkflowNamespace
	}

	// 构建 Unstructured 对象
	workflow := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": fmt.Sprintf("%s/%s", ArgoWorkflowGroup, ArgoWorkflowVersion),
			"kind":       "Workflow",
			"metadata": map[string]interface{}{
				"name":        workflowDef.Name,
				"namespace":   workflowDef.Namespace,
				"labels":      workflowDef.Labels,
				"annotations": workflowDef.Annotations,
			},
			"spec": workflowDef.Spec,
		},
	}

	// 提交工作流
	result, err := dynamicClient.Resource(workflowResource).Namespace(workflowDef.Namespace).Create(ctx, workflow, metav1.CreateOptions{})
	if err != nil {
		return "", fmt.Errorf("创建工作流失败: %w", err)
	}

	workflowName := result.GetName()
	logger.Info("Argo 工作流提交成功", "cluster_id", kceClusterID, "workflow_name", workflowName, "namespace", workflowDef.Namespace)

	return workflowName, nil
}

// GetWorkflowStatus 获取工作流状态
func (m *ArgoWorkflowManager) GetWorkflowStatus(ctx context.Context, clusterID, namespace, workflowName string) (string, error) {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, clusterID)
	if err != nil {
		return "", fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return "", fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 获取工作流
	workflow, err := dynamicClient.Resource(workflowResource).Namespace(namespace).Get(ctx, workflowName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return "NotFound", nil
		}
		return "", fmt.Errorf("获取工作流失败: %w", err)
	}

	// 提取状态
	status, found, err := unstructured.NestedString(workflow.Object, "status", "phase")
	if err != nil {
		return "", fmt.Errorf("提取工作流状态失败: %w", err)
	}

	if !found {
		return "Pending", nil
	}

	return status, nil
}

// CancelWorkflow 取消工作流
func (m *ArgoWorkflowManager) CancelWorkflow(ctx context.Context, clusterID, namespace, workflowName string) error {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, clusterID)
	if err != nil {
		return fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 删除工作流
	err = dynamicClient.Resource(workflowResource).Namespace(namespace).Delete(ctx, workflowName, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("删除工作流失败: %w", err)
	}

	logger.Info("Argo 工作流取消成功", "cluster_id", clusterID, "workflow_name", workflowName, "namespace", namespace)
	return nil
}

// CreateClusterWorkflow/Deletion/Spark/Flink workflows 已迁移至独立文件

// getRestConfig 获取 REST 配置
func (m *ArgoWorkflowManager) getRestConfig(ctx context.Context, clusterID string) (*rest.Config, error) {
	// 通过客户端管理器获取集群配置
	client, err := m.clientManager.GetClient(ctx, clusterID)
	if err != nil {
		return nil, fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 获取客户端配置
	config, err := m.clientManager.GetConfig(ctx, clusterID)
	if err != nil {
		return nil, fmt.Errorf("获取集群配置失败: %w", err)
	}

	// 使用客户端确保连接可用
	_, err = client.Discovery().ServerVersion()
	if err != nil {
		return nil, fmt.Errorf("验证集群连接失败: %w", err)
	}

	return config, nil
}

// ensureArgoNamespace 确保 Argo 命名空间存在，并安装 Argo Workflows（如果未安装）
func (m *ArgoWorkflowManager) ensureArgoNamespace(ctx context.Context, client *kubernetes.Clientset, kceClusterID string, namespace string) error {
	// 检查 Argo Workflows 是否已安装
	installed, err := m.isArgoWorkflowsInstalled(ctx, client, namespace)
	if err != nil {
		return fmt.Errorf("检查 Argo Workflows 安装状态失败: %w", err)
	}

	if !installed {
		// Argo Workflows 未安装，使用 Helm 仓库安装
		logger.Info("Argo Workflows 未安装，开始安装", "namespace", namespace)
		if err := m.installArgoWorkflowsFromRepo(ctx, kceClusterID, namespace); err != nil {
			return fmt.Errorf("安装 Argo Workflows 失败: %w", err)
		}
		logger.Info("Argo Workflows 安装成功", "namespace", namespace)
	} else {
		logger.Info("Argo Workflows 已安装", "namespace", namespace)
	}

	// 确保命名空间存在
	_, err = client.CoreV1().Namespaces().Get(ctx, namespace, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建命名空间
			ns := &corev1.Namespace{
				ObjectMeta: metav1.ObjectMeta{
					Name: namespace,
				},
			}
			_, err = client.CoreV1().Namespaces().Create(ctx, ns, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("创建命名空间失败: %w", err)
			}
			logger.Info("创建 Argo 命名空间成功", "namespace", namespace)
		} else {
			return fmt.Errorf("获取命名空间失败: %w", err)
		}
	}
	return nil
}

// getSparkOperatorInstallTemplates 获取 Spark Operator 安装模板集合
// getSparkOperatorInstallTemplates 已迁移至 spark_workflow.go

// getFlinkOperatorInstallTemplates 获取 Flink Operator 安装模板集合
// getFlinkOperatorInstallTemplates 已迁移至 flink_workflow.go

// CreateOperatorInstallWorkflow 已迁移至 operators_workflow.go

// RetryWorkflowStep 重试工作流步骤
func (m *ArgoWorkflowManager) RetryWorkflowStep(ctx context.Context, clusterID, namespace, workflowName, stepName string) error {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, clusterID)
	if err != nil {
		return fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 获取当前工作流
	workflow, err := dynamicClient.Resource(workflowResource).Namespace(namespace).Get(ctx, workflowName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取工作流失败: %w", err)
	}

	// 重置指定步骤的状态
	// 这里是重试逻辑的简化版本，实际实现需要更复杂的状态管理
	annotations := workflow.GetAnnotations()
	if annotations == nil {
		annotations = make(map[string]string)
	}
	annotations["workflows.argoproj.io/retry-step"] = stepName

	workflow.SetAnnotations(annotations)

	// 更新工作流
	_, err = dynamicClient.Resource(workflowResource).Namespace(namespace).Update(ctx, workflow, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新工作流失败: %w", err)
	}

	logger.Info("工作流步骤重试成功", "cluster_id", clusterID, "workflow_name", workflowName, "step_name", stepName)
	return nil
}

// isArgoWorkflowsInstalled 检查 Argo Workflows 是否已安装
func (m *ArgoWorkflowManager) isArgoWorkflowsInstalled(ctx context.Context, client *kubernetes.Clientset, namespace string) (bool, error) {
	// 检查 workflow-controller deployment 是否存在
	_, err := client.AppsV1().Deployments(namespace).Get(ctx, "argo-workflows-workflow-controller", metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return false, nil
		}
		return false, fmt.Errorf("检查 workflow-controller deployment 失败: %w", err)
	}

	// 检查 argo-server deployment 是否存在（可选组件）
	_, err = client.AppsV1().Deployments(namespace).Get(ctx, "argo-server", metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			logger.Info("Argo Server 未安装，仅检查到 workflow-controller", "namespace", namespace)
		} else {
			logger.Warn("检查 argo-server deployment 失败", "error", err.Error())
		}
	}

	return true, nil
}

// installArgoWorkflowsFromLocal 使用本地 Helm Chart 安装 Argo Workflows
func (m *ArgoWorkflowManager) installArgoWorkflowsFromRepo(ctx context.Context, kceClusterID string, namespace string) error {
	kubeConfigBytes, err := utils.GetKubernetesConfig(kceClusterID)
	if err != nil {
		return fmt.Errorf("获取 kubeconfig 失败: %w", err)
	}

	// 使用本地 Helm Chart 路径
	chartPath := "./deploy/helm/argo-workflows"
	releaseName := "argo-workflows"
	version := "1.0.0"

	// 为不同的集群生成不同的 ingress 配置
	values := m.generateArgoWorkflowsValues(kceClusterID)

	// 调用 InstallHelmChartFromLocal 安装 Argo Workflows
	err = innerKubernetes.InstallHelmChartFromLocal(kubeConfigBytes, namespace, chartPath, releaseName, version, values)
	if err != nil {
		return fmt.Errorf("使用 Helm 安装 Argo Workflows 失败: %w", err)
	}

	return m.waitForArgoWorkflowsReady(ctx, kceClusterID, namespace)
}

// waitForArgoWorkflowsReady 等待 Argo Workflows 部署就绪
func (m *ArgoWorkflowManager) waitForArgoWorkflowsReady(ctx context.Context, kceClusterID string, namespace string) error {
	logger.Info("等待 Argo Workflows 部署就绪", "namespace", namespace)

	// 获取 Kubernetes 客户端
	client, err := m.clientManager.GetClient(ctx, kceClusterID)
	if err != nil {
		return fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 等待 workflow-controller 就绪
	for i := 0; i < 30; i++ { // 最多等待 5 分钟
		deployment, err := client.AppsV1().Deployments(namespace).Get(ctx, "argo-workflows-workflow-controller", metav1.GetOptions{})
		if err != nil {
			if !errors.IsNotFound(err) {
				return fmt.Errorf("获取 workflow-controller deployment 失败: %w", err)
			}
		} else {
			if deployment.Status.ReadyReplicas > 0 {
				logger.Info("workflow-controller 已就绪", "namespace", namespace)
				break
			}
		}

		if i == 29 {
			return fmt.Errorf("等待 workflow-controller 就绪超时")
		}

		logger.Info("等待 workflow-controller 就绪中...", "attempt", i+1, "namespace", namespace)
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(10 * time.Second):
		}
	}

	logger.Info("Argo Workflows 部署已就绪", "namespace", namespace)
	return nil
}

// generateArgoWorkflowsValues 为指定集群生成 Argo Workflows Helm values 配置
func (m *ArgoWorkflowManager) generateArgoWorkflowsValues(kceClusterID string) string {
	// 基于集群ID生成唯一的主机名，使用集群ID的前8位字符
	shortID := kceClusterID
	if len(shortID) > 8 {
		shortID = shortID[:8]
	}
	hostname := fmt.Sprintf("argo-workflows-%s.kmr-on-kce-pre.ksyun.com", shortID)

	values := fmt.Sprintf(`
server:
  enabled: true
  ingress:
    enabled: true
    ingressClassName: "kmr-on-kce"
    hosts:
      - %s
    paths:
      - /
    pathType: Prefix
    annotations:
      nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
      kubernetes.io/ingress.class: "kmr-on-kce"
  # 启用服务器认证模式
  authModes:
    - server
  # 额外参数以支持不安全模式访问
  extraArgs:
    - --auth-mode=server
    - --secure=false

# 禁用工作流存档以简化配置
controller:
  persistence: {}
  
# 工作流服务账号配置
workflow:
  serviceAccount:
    create: true
    name: argo-workflow
  rbac:
    create: true
`, hostname)

	return values
}

// GetArgoWorkflowsWebUIURL 获取指定集群的 Argo Workflows Web UI 访问地址
func (m *ArgoWorkflowManager) GetArgoWorkflowsWebUIURL(kceClusterID string) string {
	// 基于集群ID生成唯一的主机名，使用集群ID的前8位字符
	shortID := kceClusterID
	if len(shortID) > 8 {
		shortID = shortID[:8]
	}
	hostname := fmt.Sprintf("argo-workflows-%s.kmr-on-kce-pre.ksyun.com", shortID)
	return fmt.Sprintf("https://%s", hostname)
}
