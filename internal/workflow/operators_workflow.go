package workflow

// CreateOperatorInstallWorkflow 创建 Operator 安装工作流
func (m *ArgoWorkflowManager) CreateOperatorInstallWorkflow(operatorType, clusterID string) WorkflowDefinition {
	switch operatorType {
	case "spark":
		return m.createSparkOperatorWorkflow("install-spark-operator", clusterID)
	case "flink":
		return m.createFlinkOperatorWorkflow("install-flink-operator", clusterID)
	default:
		return WorkflowDefinition{}
	}
}
