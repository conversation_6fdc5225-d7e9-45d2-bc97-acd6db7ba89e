package workflow

import (
	"fmt"
	"strings"

	"github.com/google/uuid"
)

// CreateClusterWorkflow 创建集群管理工作流
func (m *ArgoWorkflowManager) CreateClusterWorkflow(operationType, clusterID, clusterName, kceClusterID string, services []string, ak, sk, logDirectory string) WorkflowDefinition {
	workflowName := fmt.Sprintf("%s-cluster-%s-%s", operationType, clusterName, uuid.New().String()[:8])

	switch operationType {
	case "create":
		return m.createClusterCreationWorkflow(workflowName, clusterID, clusterName, kceClusterID, services, ak, sk, logDirectory)
	case "delete":
		return m.createClusterDeletionWorkflow(workflowName, clusterID, clusterName, kceClusterID, services)
	default:
		return WorkflowDefinition{}
	}
}

// createClusterCreationWorkflow 创建集群创建工作流
func (m *ArgoWorkflowManager) createClusterCreationWorkflow(workflowName, clusterID, clusterName, kceClusterID string, services []string, ak, sk, logDirectory string) WorkflowDefinition {
	// 构建服务安装步骤
	serviceSteps := make([]WorkflowStep, 0, len(services))
	for _, service := range services {
		serviceSteps = append(serviceSteps, WorkflowStep{
			Name:     fmt.Sprintf("install-%s-operator", service),
			Template: fmt.Sprintf("install-%s-operator-template", service),
			Arguments: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "cluster-id", Value: "{{workflow.parameters.cluster-id}}"},
					{Name: "kce-cluster-id", Value: "{{workflow.parameters.kce-cluster-id}}"},
					{Name: "service-type", Value: service},
					{Name: "ak", Value: "{{workflow.parameters.ak}}"},
					{Name: "sk", Value: "{{workflow.parameters.sk}}"},
					{Name: "logDirectory", Value: "{{workflow.parameters.logDirectory}}"},
				},
			},
		})
	}

	templates := []WorkflowTemplate{
		{
			Name: "cluster-creation-main",
			Steps: [][]WorkflowStep{
				// 第1步：验证集群连接
				{
					{
						Name:     "validate-cluster",
						Template: "validate-cluster-connection",
						Arguments: &WorkflowInputs{
							Parameters: []WorkflowParameter{
								{Name: "kce-cluster-id", Value: "{{workflow.parameters.kce-cluster-id}}"},
							},
						},
					},
				},
				// 第2步：创建基础资源
				{
					{
						Name:     "create-base-resources",
						Template: "create-base-resources",
						Arguments: &WorkflowInputs{
							Parameters: []WorkflowParameter{
								{Name: "cluster-id", Value: "{{workflow.parameters.cluster-id}}"},
								{Name: "cluster-name", Value: "{{workflow.parameters.cluster-name}}"},
							},
						},
					},
				},
				// 第3步：并行安装服务（如果有的话）
				serviceSteps,
				// 第4步：验证部署状态
				{
					{
						Name:     "verify-deployments",
						Template: "verify-all-deployments",
						Arguments: &WorkflowInputs{
							Parameters: []WorkflowParameter{
								{Name: "services", Value: "{{workflow.parameters.services}}"},
							},
						},
					},
				},
				// 第5步：更新集群状态
				{
					{
						Name:     "update-cluster-status",
						Template: "update-cluster-status",
						Arguments: &WorkflowInputs{
							Parameters: []WorkflowParameter{
								{Name: "cluster-id", Value: "{{workflow.parameters.cluster-id}}"},
								{Name: "status", Value: "active"},
								{Name: "services", Value: "{{workflow.parameters.services}}"},
							},
						},
					},
				},
			},
		},
		// 验证集群连接模板
		{
			Name: "validate-cluster-connection",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始验证KCE集群连接: {{inputs.parameters.kce-cluster-id}}"

# 验证kubectl可以连接到集群
echo "检查集群基本信息..."
kubectl cluster-info --request-timeout=30s

echo "检查集群节点状态..."
kubectl get nodes --no-headers | head -5

echo "检查集群版本信息..."
kubectl version

echo "验证集群命名空间..."
kubectl get namespaces

echo "✓ KCE集群 {{inputs.parameters.kce-cluster-id}} 连接验证成功"
`,
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "kce-cluster-id"},
				},
			},
		},
		// 创建基础资源模板
		{
			Name: "create-base-resources",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "Creating base resources for cluster {{inputs.parameters.cluster-name}}"

# 创建必要的命名空间
kubectl create namespace nimbus-system --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace kmrspark --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace kmrflink --dry-run=client -o yaml | kubectl apply -f -

# 创建基础 ConfigMap
kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-info
  namespace: nimbus-system
data:
  cluster-id: "{{inputs.parameters.cluster-id}}"
  cluster-name: "{{inputs.parameters.cluster-name}}"
  created-at: "$(date -Iseconds)"
EOF

echo "Base resources created successfully"
`,
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "cluster-id"},
					{Name: "cluster-name"},
				},
			},
		},
		// 验证所有部署模板
		{
			Name: "verify-all-deployments",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

services="{{inputs.parameters.services}}"
echo "验证部署状态，服务列表: $services"

# 检查 Spark Operator
if echo "$services" | grep -q "spark"; then
    echo "验证 Spark Operator 部署状态..."
    kubectl wait --for=condition=available --timeout=300s deployment/spark-operator -n kmrspark
    echo "✓ Spark Operator 验证成功"

    # 检查Spark History是否安装
    if kubectl get deployment spark-history -n kmrspark >/dev/null 2>&1; then
        echo "验证 Spark History 部署状态..."
        kubectl wait --for=condition=available --timeout=300s deployment/spark-history -n kmrspark
        echo "✓ Spark History 验证成功"
    else
        echo "⚠ Spark History 未安装，跳过验证"
    fi
fi

# 检查 Flink Operator
if echo "$services" | grep -q "flink"; then
    echo "验证 Flink Operator 部署状态..."
    kubectl wait --for=condition=available --timeout=300s deployment/flink-kubernetes-operator -n kmrflink
    echo "✓ Flink Operator 验证成功"
fi

echo "✓ 所有部署验证完成"
`,
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "services"},
				},
			},
		},
		// 更新集群状态模板
		{
			Name: "update-cluster-status",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "更新集群状态为 active..."

# 尝试调用API更新集群状态（如果nimbus服务在集群内运行）
echo "尝试调用nimbus API更新集群状态..."
if curl -f --connect-timeout 10 --max-time 30 -X PUT "http://nimbus-api-service:8080/api/v1/clusters/{{inputs.parameters.cluster-id}}" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "active",
    "services": "{{inputs.parameters.services}}"
  }' > /dev/null 2>&1; then
    echo "✓ 集群状态通过API更新成功"
else
    echo "⚠ 无法访问nimbus API服务（可能运行在集群外），跳过API更新"
    echo "这是正常情况，如果nimbus服务运行在本地"
fi

# 同时创建集群状态 ConfigMap 作为备份
kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-status
  namespace: nimbus-system
data:
  status: "active"
  updated-at: "$(date -Iseconds)"
  services: "{{inputs.parameters.services}}"
EOF

echo "集群状态更新完成"
`,
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "cluster-id"},
					{Name: "services"},
				},
			},
		},
	}

	// 添加服务安装模板
	if len(services) > 0 {
		for _, service := range services {
			switch service {
			case "spark":
				// spark 还会在 spark 模板中安装 spark-history
				templates = append(templates, m.getSparkOperatorInstallTemplates()...)
			case "flink":
				templates = append(templates, m.getFlinkOperatorInstallTemplates()...)
			}
		}
	}

	return WorkflowDefinition{
		Name:      workflowName,
		Namespace: ArgoWorkflowNamespace,
		Labels: map[string]string{
			"app":          "nimbus",
			"operation":    "cluster-create",
			"cluster-id":   clusterID,
			"cluster-name": clusterName,
		},
		Annotations: map[string]string{
			"nimbus.io/cluster-id":     clusterID,
			"nimbus.io/kce-cluster-id": kceClusterID,
			"nimbus.io/services":       fmt.Sprintf("[%s]", strings.Join(services, ",")),
		},
		Spec: WorkflowSpec{
			Entrypoint:         "cluster-creation-main",
			ServiceAccountName: "kubectl-admin-sa",
			Templates:          templates,
			Arguments: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "cluster-id", Value: clusterID},
					{Name: "cluster-name", Value: clusterName},
					{Name: "kce-cluster-id", Value: kceClusterID},
					{Name: "services", Value: fmt.Sprintf("[%s]", strings.Join(services, ","))},
					{Name: "ak", Value: ak},
					{Name: "sk", Value: sk},
					{Name: "logDirectory", Value: logDirectory},
				},
			},
		},
	}
}

// createClusterDeletionWorkflow 创建集群删除工作流
func (m *ArgoWorkflowManager) createClusterDeletionWorkflow(workflowName, clusterID, clusterName, kceClusterID string, services []string) WorkflowDefinition {
	// 根据已安装服务构建需要卸载的步骤（不再依赖 when 表达式）
	deleteOperatorSteps := make([]WorkflowStep, 0, 2)
	for _, svc := range services {
		switch svc {
		case "spark":
			deleteOperatorSteps = append(deleteOperatorSteps, WorkflowStep{
				Name:     "uninstall-spark-operator",
				Template: "uninstall-spark-operator",
			})
		case "flink":
			deleteOperatorSteps = append(deleteOperatorSteps, WorkflowStep{
				Name:     "uninstall-flink-operator",
				Template: "uninstall-flink-operator",
			})
		}
	}
	return WorkflowDefinition{
		Name:      workflowName,
		Namespace: ArgoWorkflowNamespace,
		Labels: map[string]string{
			"app":        "nimbus",
			"operation":  "cluster-delete",
			"cluster-id": clusterID,
		},
		Annotations: map[string]string{
			"nimbus.io/cluster-id":     clusterID,
			"nimbus.io/kce-cluster-id": kceClusterID,
		},
		Spec: WorkflowSpec{
			Entrypoint:         "cluster-deletion-main",
			ServiceAccountName: "kubectl-admin-sa",
			Templates: []WorkflowTemplate{
				{
					Name: "cluster-deletion-main",
					Steps: [][]WorkflowStep{
						// 第1步：根据已记录服务卸载对应 Operators
						deleteOperatorSteps,
						// 第2步：清理资源
						{
							{
								Name:     "cleanup-resources",
								Template: "cleanup-cluster-resources",
								Arguments: &WorkflowInputs{
									Parameters: []WorkflowParameter{
										{Name: "cluster-id", Value: clusterID},
									},
								},
							},
						},
						// 第3步：更新集群状态
						{
							{
								Name:     "mark-cluster-deleted",
								Template: "mark-cluster-deleted",
								Arguments: &WorkflowInputs{
									Parameters: []WorkflowParameter{
										{Name: "cluster-id", Value: clusterID},
									},
								},
							},
						},
					},
				},
				// 卸载 Spark Operator 模板
				{
					Name: "uninstall-spark-operator",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "Uninstalling Spark Operator..."

# 检查 Spark Operator 是否存在
if kubectl get namespace kmrspark >/dev/null 2>&1; then
    echo "Found Spark Operator, proceeding with uninstallation..."
    
    # 删除 Spark Operator 部署
    kubectl delete deployment spark-operator -n kmrspark --ignore-not-found=true
    
    # 删除 RBAC 资源
    kubectl delete clusterrolebinding spark-operator --ignore-not-found=true
    kubectl delete clusterrole spark-operator --ignore-not-found=true
    kubectl delete serviceaccount spark-operator -n kmrspark --ignore-not-found=true
    
    # 删除命名空间
    kubectl delete namespace kmrspark --ignore-not-found=true
    
    echo "Spark Operator uninstalled successfully"
else
    echo "Spark Operator not found, skipping uninstallation"
fi
`,
					},
				},
				// 卸载 Flink Operator 模板
				{
					Name: "uninstall-flink-operator",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "Uninstalling Flink Operator..."

# 检查 Flink Operator 是否存在
if kubectl get namespace kmrflink >/dev/null 2>&1; then
    echo "Found Flink Operator, proceeding with uninstallation..."
    
    # 删除 Flink Operator 部署
    kubectl delete deployment flink-kubernetes-operator -n kmrflink --ignore-not-found=true
    
    # 删除 RBAC 资源
    kubectl delete clusterrolebinding flink-operator --ignore-not-found=true
    kubectl delete clusterrole flink-operator --ignore-not-found=true
    kubectl delete serviceaccount flink-operator -n kmrflink --ignore-not-found=true
    
    # 删除命名空间
    kubectl delete namespace kmrflink --ignore-not-found=true
    
    echo "Flink Operator uninstalled successfully"
else
    echo "Flink Operator not found, skipping uninstallation"
fi
`,
					},
				},
				// 清理集群资源模板
				{
					Name: "cleanup-cluster-resources",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "清理集群相关资源..."
echo "集群ID: {{inputs.parameters.cluster-id}}"

# 只删除与特定集群相关的命名空间，不删除系统命名空间
echo "删除集群相关的工作负载命名空间..."
kubectl delete namespace kmrspark --ignore-not-found=true
kubectl delete namespace kmrflink --ignore-not-found=true

# 清理集群特定的资源（使用集群ID标签）
echo "清理集群特定的资源..."
CLUSTER_ID="{{inputs.parameters.cluster-id}}"
if [ -n "$CLUSTER_ID" ]; then
    # 清理带有集群ID标签的资源
    kubectl delete all -l cluster-id="$CLUSTER_ID" --all-namespaces --ignore-not-found=true
    kubectl delete configmap -l cluster-id="$CLUSTER_ID" --all-namespaces --ignore-not-found=true
    kubectl delete secret -l cluster-id="$CLUSTER_ID" --all-namespaces --ignore-not-found=true

    echo "✓ 已清理集群 $CLUSTER_ID 的相关资源"
else
    echo "⚠ 集群ID为空，跳过资源清理"
fi

echo "⚠ 保留 nimbus-system 命名空间（包含系统服务）"

echo "✓ 集群资源清理完成"
`,
					},
					Inputs: &WorkflowInputs{
						Parameters: []WorkflowParameter{
							{Name: "cluster-id"},
						},
					},
				},
				// 标记集群已删除模板
				{
					Name: "mark-cluster-deleted",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "标记集群为已删除状态..."

# 尝试调用内部API删除集群记录（如果nimbus服务在集群内运行）
echo "尝试调用nimbus API删除集群记录..."
if curl -f --connect-timeout 10 --max-time 30 -X DELETE "http://nimbus-api-service:8080/api/v1/clusters/{{inputs.parameters.cluster-id}}" \
  -H "Content-Type: application/json" > /dev/null 2>&1; then
    echo "✓ 集群记录通过API删除成功"
else
    echo "⚠ 无法访问nimbus API服务（可能运行在集群外），跳过API删除"
    echo "这是正常情况，如果nimbus服务运行在本地"
fi

# 创建集群删除状态的ConfigMap作为备份记录
kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-deletion-record
  namespace: nimbus-system
data:
  cluster-id: "{{inputs.parameters.cluster-id}}"
  status: "deleted"
  deleted-at: "$(date -Iseconds)"
EOF

echo "✓ 集群删除状态标记完成"
`,
					},
					Inputs: &WorkflowInputs{
						Parameters: []WorkflowParameter{
							{Name: "cluster-id"},
						},
					},
				},
			},
		},
	}
}
