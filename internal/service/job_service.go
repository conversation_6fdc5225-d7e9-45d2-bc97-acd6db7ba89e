package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/internal/kubernetes"
	"github.com/kingsoft/nimbus/internal/model"
	"github.com/kingsoft/nimbus/internal/repository"
	"github.com/kingsoft/nimbus/internal/workflow"
	"github.com/kingsoft/nimbus/pkg/logger"
)

// JobService 任务服务接口
type JobService interface {
	Create(name, jobType, namespace, accountID string, clusterID uuid.UUID, config map[string]interface{}, command, args string) (*model.Job, error)
	GetByID(id uuid.UUID) (*model.Job, error)
	Update(id uuid.UUID, status string) (*model.Job, error)
	Delete(id uuid.UUID) error
	List(offset, limit int, clusterID *uuid.UUID, accountID, jobType, status string) ([]*model.Job, int64, error)
	Submit(ctx context.Context, id uuid.UUID) error
	Cancel(ctx context.Context, id uuid.UUID) error
}

// jobService 任务服务实现
type jobService struct {
	jobRepo          repository.JobRepository
	clusterRepo      repository.ClusterRepository
	auditLogRepo     repository.AuditLogRepository
	clientManager    *kubernetes.ClientManager
	jobSubmissionMgr *kubernetes.JobSubmissionManager
	workflowManager  *workflow.ArgoWorkflowManager
	workflowRepo     repository.WorkflowRepository
}

// NewJobService 创建任务服务
func NewJobService(
	jobRepo repository.JobRepository,
	clusterRepo repository.ClusterRepository,
	auditLogRepo repository.AuditLogRepository,
	clientManager *kubernetes.ClientManager,
	workflowManager *workflow.ArgoWorkflowManager,
	workflowRepo repository.WorkflowRepository,
) JobService {
	return &jobService{
		jobRepo:          jobRepo,
		clusterRepo:      clusterRepo,
		auditLogRepo:     auditLogRepo,
		clientManager:    clientManager,
		jobSubmissionMgr: kubernetes.NewJobSubmissionManager(clientManager),
		workflowManager:  workflowManager,
		workflowRepo:     workflowRepo,
	}
}

// Create 创建任务
func (s *jobService) Create(name, jobType, namespace, AccountID string, clusterID uuid.UUID, config map[string]interface{}, command, args string) (*model.Job, error) {
	// 检查集群是否存在
	_, err := s.clusterRepo.GetByID(clusterID)
	if err != nil {
		return nil, fmt.Errorf("集群不存在: %w", err)
	}

	// 序列化配置
	configJSON, err := json.Marshal(config)
	if err != nil {
		return nil, fmt.Errorf("序列化配置失败: %w", err)
	}

	// 创建任务
	job := &model.Job{
		Name:      name,
		Type:      jobType,
		Status:    model.JobStatusPending,
		ClusterID: clusterID,
		AccountID: AccountID,
		Namespace: namespace,
		Config:    string(configJSON),
		Command:   command,
		Args:      args,
		StartTime: time.Time{},
		EndTime:   time.Time{},
	}

	if err := s.jobRepo.Create(job); err != nil {
		return nil, fmt.Errorf("创建任务失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(AccountID, model.AuditActionCreate, model.AuditResourceJob, job.ID.String(), fmt.Sprintf("创建%s任务", jobType))

	logger.Info("任务创建成功", "job_id", job.ID, "cluster_id", clusterID)

	return job, nil
}

// GetByID 通过ID获取任务
func (s *jobService) GetByID(id uuid.UUID) (*model.Job, error) {
	return s.jobRepo.GetByID(id)
}

// Update 更新任务状态
func (s *jobService) Update(id uuid.UUID, status string) (*model.Job, error) {
	// 获取任务
	job, err := s.jobRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("任务不存在: %w", err)
	}

	// 更新任务状态
	job.Status = status
	if status == model.JobStatusRunning && job.StartTime.IsZero() {
		job.StartTime = time.Now()
	}
	if (status == model.JobStatusCompleted || status == model.JobStatusFailed || status == model.JobStatusCancelled) && job.EndTime.IsZero() {
		job.EndTime = time.Now()
	}

	// 更新任务
	if err := s.jobRepo.Update(job); err != nil {
		return nil, fmt.Errorf("更新任务失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(job.AccountID, model.AuditActionUpdate, model.AuditResourceJob, job.ID.String(), fmt.Sprintf("更新任务状态为%s", status))

	return job, nil
}

// Delete 删除任务
func (s *jobService) Delete(id uuid.UUID) error {
	// 获取任务
	job, err := s.jobRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("任务不存在: %w", err)
	}

	// 删除任务
	if err := s.jobRepo.Delete(id); err != nil {
		return fmt.Errorf("删除任务失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(job.AccountID, model.AuditActionDelete, model.AuditResourceJob, job.ID.String(), "删除任务")

	return nil
}

// List 获取任务列表
func (s *jobService) List(offset, limit int, clusterID *uuid.UUID, accountID, jobType, status string) ([]*model.Job, int64, error) {
	return s.jobRepo.List(offset, limit, clusterID, accountID, jobType, status)
}

// Submit 提交任务
func (s *jobService) Submit(ctx context.Context, id uuid.UUID) error {
	// 获取任务
	job, err := s.jobRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("任务不存在: %w", err)
	}

	// 获取集群
	cluster, err := s.clusterRepo.GetByID(job.ClusterID)
	if err != nil {
		return fmt.Errorf("集群不存在: %w", err)
	}

	// 更新任务状态
	job.Status = model.JobStatusRunning
	job.StartTime = time.Now()
	if err := s.jobRepo.Update(job); err != nil {
		logger.Error("更新任务状态失败", "job_id", job.ID, "error", err)
	}

	// 解析任务配置
	var config map[string]interface{}
	if job.Config != "" {
		if err := json.Unmarshal([]byte(job.Config), &config); err != nil {
			return fmt.Errorf("解析任务配置失败: %w", err)
		}
	}

	// 构建任务规格
	jobSpec := kubernetes.JobSpec{
		JobID:      job.ID.String(),
		JobType:    job.Type,
		JobName:    fmt.Sprintf("nimbus-%s-%s", job.Type, job.ID.String()[:8]),
		Namespace:  job.Namespace,
		Image:      getConfigValue(config, "image", getDefaultImage(job.Type)),
		MainClass:  getConfigValue(config, "main_class", ""),
		JarFile:    getConfigValue(config, "jar_file", ""),
		PythonFile: getConfigValue(config, "python_file", ""),
		Args:       parseArgs(job.Args),
		Env:        extractEnvVars(config),
		Resources:  extractResourceSpec(config, job.Type),
		Config:     extractJobConfig(config),
	}

	// 使用工作流提交任务
	workflowName := fmt.Sprintf("submit-job-%s", job.ID.String()[:8])

	// 创建工作流定义
	workflowDef := s.workflowManager.CreateSparkJobWorkflow(
		workflowName,
		cluster.KceClusterID,
		jobSpec.JobName,
		jobSpec.Namespace,
		map[string]interface{}{
			"jobSpec": jobSpec,
		},
	)

	// 序列化工作流定义
	definitionJSON, err := json.Marshal(workflowDef)
	if err != nil {
		// 更新任务状态为失败
		job.Status = model.JobStatusFailed
		job.EndTime = time.Now()
		if updateErr := s.jobRepo.Update(job); updateErr != nil {
			logger.Error("更新任务状态失败", "job_id", job.ID, "error", updateErr)
		}
		return fmt.Errorf("序列化工作流定义失败: %w", err)
	}

	// 创建工作流记录
	workflow := &model.Workflow{
		Name:        workflowName,
		Description: fmt.Sprintf("提交任务: %s", job.Name),
		Status:      model.WorkflowStatusPending,
		Definition:  string(definitionJSON),
		ClusterID:   cluster.ID,
		AccountID:   job.AccountID,
		Namespace:   "argo",
		StartTime:   time.Time{},
		EndTime:     time.Time{},
	}

	if err := s.workflowRepo.Create(workflow); err != nil {
		// 更新任务状态为失败
		job.Status = model.JobStatusFailed
		job.EndTime = time.Now()
		if updateErr := s.jobRepo.Update(job); updateErr != nil {
			logger.Error("更新任务状态失败", "job_id", job.ID, "error", updateErr)
		}
		return fmt.Errorf("创建工作流记录失败: %w", err)
	}

	// 提交工作流到 Argo
	argoWorkflowName, err := s.workflowManager.SubmitWorkflow(ctx, cluster.KceClusterID, workflowDef)
	if err != nil {
		// 更新工作流状态为失败
		workflow.Status = model.WorkflowStatusFailed
		workflow.EndTime = time.Now()
		s.workflowRepo.Update(workflow)

		// 更新任务状态为失败
		job.Status = model.JobStatusFailed
		job.EndTime = time.Now()
		if updateErr := s.jobRepo.Update(job); updateErr != nil {
			logger.Error("更新任务状态失败", "job_id", job.ID, "error", updateErr)
		}
		return fmt.Errorf("提交工作流到 Argo 失败: %w", err)
	}

	// 更新工作流的 ResourceID
	workflow.ResourceID = argoWorkflowName
	workflow.Status = model.WorkflowStatusRunning
	workflow.StartTime = time.Now()
	if err := s.workflowRepo.Update(workflow); err != nil {
		logger.Error("更新工作流状态失败", "workflow_id", workflow.ID, "error", err)
	}

	logger.Info("任务提交工作流创建成功", "job_id", job.ID, "cluster_id", cluster.ID, "workflow_id", workflow.ID, "argo_workflow", argoWorkflowName)

	// 记录审计日志
	s.logAudit(job.AccountID, model.AuditActionUpdate, model.AuditResourceJob, job.ID.String(), "提交任务")

	return nil
}

// Cancel 取消任务
func (s *jobService) Cancel(ctx context.Context, id uuid.UUID) error {
	// 获取任务
	job, err := s.jobRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("任务不存在: %w", err)
	}

	// 获取集群
	cluster, err := s.clusterRepo.GetByID(job.ClusterID)
	if err != nil {
		return fmt.Errorf("集群不存在: %w", err)
	}

	// 取消 Kubernetes 中的任务
	jobName := fmt.Sprintf("nimbus-%s-%s", job.Type, job.ID.String()[:8])
	err = s.jobSubmissionMgr.CancelJob(ctx, cluster.KceClusterID, job.Type, job.Namespace, jobName)
	if err != nil {
		logger.Error("取消 Kubernetes 任务失败", "job_id", job.ID, "error", err)
		// 即使取消失败，也要更新本地状态
	}

	// 更新任务状态
	job.Status = model.JobStatusCancelled
	job.EndTime = time.Now()
	if err := s.jobRepo.Update(job); err != nil {
		logger.Error("更新任务状态失败", "job_id", job.ID, "error", err)
	}

	logger.Info("任务取消成功", "job_id", job.ID, "cluster_id", cluster.ID)

	// 记录审计日志
	s.logAudit(job.AccountID, model.AuditActionUpdate, model.AuditResourceJob, job.ID.String(), "取消任务")

	return nil
}

// logAudit 记录审计日志
func (s *jobService) logAudit(accountID, action, resource, resourceID, detail string) {
	auditLog := &model.AuditLog{
		AccountID:  accountID,
		Action:     action,
		Resource:   resource,
		ResourceID: resourceID,
		Detail:     detail,
	}

	if err := s.auditLogRepo.Create(auditLog); err != nil {
		logger.Error("记录审计日志失败", "account_id", accountID, "action", action, "resource", resource, "error", err)
	}
}

// 辅助函数

// getConfigValue 获取配置值
func getConfigValue(config map[string]interface{}, key, defaultValue string) string {
	if config == nil {
		return defaultValue
	}

	if value, exists := config[key]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}

	return defaultValue
}

// getDefaultImage 获取默认镜像
func getDefaultImage(jobType string) string {
	switch jobType {
	case "spark":
		return "apache/spark:3.1.1"
	case "flink":
		return "apache/flink:1.15.2"
	default:
		return "busybox:latest"
	}
}

// parseArgs 解析参数
func parseArgs(argsStr string) []string {
	if argsStr == "" {
		return nil
	}

	// 简单的空格分割，实际实现可能需要更复杂的解析
	args := strings.Fields(argsStr)
	return args
}

// extractEnvVars 提取环境变量
func extractEnvVars(config map[string]interface{}) map[string]string {
	envVars := make(map[string]string)

	if config == nil {
		return envVars
	}

	if env, exists := config["env"]; exists {
		if envMap, ok := env.(map[string]interface{}); ok {
			for k, v := range envMap {
				if str, ok := v.(string); ok {
					envVars[k] = str
				}
			}
		}
	}

	return envVars
}

// extractResourceSpec 提取资源规格
func extractResourceSpec(config map[string]interface{}, jobType string) kubernetes.ResourceSpec {
	resources := kubernetes.ResourceSpec{}

	if config == nil {
		// 返回默认资源配置
		return getDefaultResourceSpec(jobType)
	}

	if res, exists := config["resources"]; exists {
		if resMap, ok := res.(map[string]interface{}); ok {
			// Spark 资源配置
			if jobType == "spark" {
				resources.DriverCores = getStringValue(resMap, "driver_cores", "1")
				resources.DriverMemory = getStringValue(resMap, "driver_memory", "1g")
				resources.ExecutorCores = getStringValue(resMap, "executor_cores", "1")
				resources.ExecutorMemory = getStringValue(resMap, "executor_memory", "1g")
				resources.ExecutorInstances = getInt32Value(resMap, "executor_instances", 1)
			}

			// Flink 资源配置
			if jobType == "flink" {
				resources.JobManagerMemory = getStringValue(resMap, "job_manager_memory", "1Gi")
				resources.TaskManagerMemory = getStringValue(resMap, "task_manager_memory", "1Gi")
				resources.TaskSlots = getInt32Value(resMap, "task_slots", 1)
				resources.Parallelism = getInt32Value(resMap, "parallelism", 1)
			}
		}
	}

	// 如果没有配置，使用默认值
	if resources.DriverCores == "" && resources.JobManagerMemory == "" {
		return getDefaultResourceSpec(jobType)
	}

	return resources
}

// extractJobConfig 提取任务配置
func extractJobConfig(config map[string]interface{}) map[string]string {
	jobConfig := make(map[string]string)

	if config == nil {
		return jobConfig
	}

	if conf, exists := config["job_config"]; exists {
		if confMap, ok := conf.(map[string]interface{}); ok {
			for k, v := range confMap {
				if str, ok := v.(string); ok {
					jobConfig[k] = str
				}
			}
		}
	}

	return jobConfig
}

// getDefaultResourceSpec 获取默认资源规格
func getDefaultResourceSpec(jobType string) kubernetes.ResourceSpec {
	switch jobType {
	case "spark":
		return kubernetes.ResourceSpec{
			DriverCores:       "1",
			DriverMemory:      "1g",
			ExecutorCores:     "1",
			ExecutorMemory:    "1g",
			ExecutorInstances: 1,
		}
	case "flink":
		return kubernetes.ResourceSpec{
			JobManagerMemory:  "1Gi",
			TaskManagerMemory: "1Gi",
			TaskSlots:         1,
			Parallelism:       1,
		}
	default:
		return kubernetes.ResourceSpec{}
	}
}

// getStringValue 获取字符串值
func getStringValue(config map[string]interface{}, key, defaultValue string) string {
	if value, exists := config[key]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return defaultValue
}

// getInt32Value 获取 int32 值
func getInt32Value(config map[string]interface{}, key string, defaultValue int32) int32 {
	if value, exists := config[key]; exists {
		switch v := value.(type) {
		case int:
			return int32(v)
		case int32:
			return v
		case int64:
			return int32(v)
		case float64:
			return int32(v)
		case string:
			if i, err := strconv.Atoi(v); err == nil {
				return int32(i)
			}
		}
	}
	return defaultValue
}
